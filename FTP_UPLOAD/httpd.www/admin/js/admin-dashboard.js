document.addEventListener('DOMContentLoaded', function() {
  // Tab functionality
  const tabButtons = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');

  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all buttons and contents
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));

      // Add active class to clicked button and corresponding content
      button.classList.add('active');
      const tabId = button.dataset.tab;
      document.getElementById(tabId).classList.add('active');
    });
  });

  // Modal functionality
  const modal = document.getElementById('detail-modal');
  const closeModalButtons = document.querySelectorAll('.close-modal');

  closeModalButtons.forEach(button => {
    button.addEventListener('click', () => {
      modal.style.display = 'none';
    });
  });

  // Close modal when clicking outside
  window.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.style.display = 'none';
    }
  });

  // Load data from localStorage
  loadMeetingRequests();
  loadContactForms();

  // Search and filter functionality
  const meetingSearch = document.getElementById('meeting-search');
  const meetingFilter = document.getElementById('meeting-filter');
  const contactSearch = document.getElementById('contact-search');
  const contactFilter = document.getElementById('contact-filter');

  meetingSearch.addEventListener('input', () => {
    loadMeetingRequests(meetingSearch.value, meetingFilter.value);
  });

  meetingFilter.addEventListener('change', () => {
    loadMeetingRequests(meetingSearch.value, meetingFilter.value);
  });

  contactSearch.addEventListener('input', () => {
    loadContactForms(contactSearch.value, contactFilter.value);
  });

  contactFilter.addEventListener('change', () => {
    loadContactForms(contactSearch.value, contactFilter.value);
  });
});

// Load meeting requests from localStorage
function loadMeetingRequests(searchTerm = '', filterValue = 'all') {
  const meetingTableBody = document.getElementById('meeting-table-body');
  const meetingEmptyState = document.getElementById('meeting-empty-state');

  // Get meeting requests from localStorage
  let meetingRequests = JSON.parse(localStorage.getItem('meetingRequests')) || [];

  // Apply filter
  if (filterValue !== 'all') {
    const isProcessed = filterValue === 'processed';
    meetingRequests = meetingRequests.filter(request => request.processed === isProcessed);
  }

  // Apply search
  if (searchTerm) {
    const term = searchTerm.toLowerCase();
    meetingRequests = meetingRequests.filter(request =>
      request.name.toLowerCase().includes(term) ||
      (request.company && request.company.toLowerCase().includes(term))
    );
  }

  // Clear table
  meetingTableBody.innerHTML = '';

  // Show empty state if no requests
  if (meetingRequests.length === 0) {
    meetingEmptyState.style.display = 'block';
    return;
  }

  // Hide empty state
  meetingEmptyState.style.display = 'none';

  // Sort by date (newest first)
  meetingRequests.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  // Populate table
  meetingRequests.forEach((request, index) => {
    const row = document.createElement('tr');

    // Format date
    const requestDate = new Date(request.timestamp);
    const formattedDate = requestDate.toLocaleDateString('de-DE');

    row.innerHTML = `
      <td>${formattedDate}</td>
      <td>${request.name}</td>
      <td>${request.company || '-'}</td>
      <td><a href="mailto:${request.email}">${request.email}</a></td>
      <td><a href="tel:${request.phone}">${request.phone}</a></td>
      <td>${request.date} ${request.time}</td>
      <td><span class="status-badge status-${request.processed ? 'processed' : 'new'}">${request.processed ? 'Bearbeitet' : 'Neu'}</span></td>
      <td>
        <button class="action-btn view" data-id="${index}" title="Details anzeigen"><i class="fas fa-eye"></i></button>
        <button class="action-btn delete" data-id="${index}" title="Löschen"><i class="fas fa-trash"></i></button>
      </td>
    `;

    meetingTableBody.appendChild(row);
  });

  // Add event listeners to action buttons
  document.querySelectorAll('#meeting-table-body .action-btn.view').forEach(button => {
    button.addEventListener('click', () => {
      const id = parseInt(button.dataset.id);
      showMeetingDetails(meetingRequests[id]);
    });
  });

  document.querySelectorAll('#meeting-table-body .action-btn.delete').forEach(button => {
    button.addEventListener('click', () => {
      const id = parseInt(button.dataset.id);
      deleteMeetingRequest(id);
    });
  });
}

// Load contact forms from localStorage
function loadContactForms(searchTerm = '', filterValue = 'all') {
  const contactTableBody = document.getElementById('contact-table-body');
  const contactEmptyState = document.getElementById('contact-empty-state');

  // Get contact forms from localStorage
  let contactForms = JSON.parse(localStorage.getItem('contactForms')) || [];

  // Apply filter
  if (filterValue !== 'all') {
    const isProcessed = filterValue === 'processed';
    contactForms = contactForms.filter(form => form.processed === isProcessed);
  }

  // Apply search
  if (searchTerm) {
    const term = searchTerm.toLowerCase();
    contactForms = contactForms.filter(form =>
      form.name.toLowerCase().includes(term) ||
      form.email.toLowerCase().includes(term) ||
      form.subject.toLowerCase().includes(term)
    );
  }

  // Clear table
  contactTableBody.innerHTML = '';

  // Show empty state if no forms
  if (contactForms.length === 0) {
    contactEmptyState.style.display = 'block';
    return;
  }

  // Hide empty state
  contactEmptyState.style.display = 'none';

  // Sort by date (newest first)
  contactForms.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  // Populate table
  contactForms.forEach((form, index) => {
    const row = document.createElement('tr');

    // Format date
    const formDate = new Date(form.timestamp);
    const formattedDate = formDate.toLocaleDateString('de-DE');

    row.innerHTML = `
      <td>${formattedDate}</td>
      <td>${form.name}</td>
      <td>${form.email}</td>
      <td>${form.subject}</td>
      <td><span class="status-badge status-${form.processed ? 'processed' : 'new'}">${form.processed ? 'Bearbeitet' : 'Neu'}</span></td>
      <td>
        <button class="action-btn view" data-id="${index}" title="Details anzeigen"><i class="fas fa-eye"></i></button>
        <button class="action-btn delete" data-id="${index}" title="Löschen"><i class="fas fa-trash"></i></button>
      </td>
    `;

    contactTableBody.appendChild(row);
  });

  // Add event listeners to action buttons
  document.querySelectorAll('#contact-table-body .action-btn.view').forEach(button => {
    button.addEventListener('click', () => {
      const id = parseInt(button.dataset.id);
      showContactDetails(contactForms[id]);
    });
  });

  document.querySelectorAll('#contact-table-body .action-btn.delete').forEach(button => {
    button.addEventListener('click', () => {
      const id = parseInt(button.dataset.id);
      deleteContactForm(id);
    });
  });
}

// Show meeting request details in modal
function showMeetingDetails(request) {
  const modal = document.getElementById('detail-modal');
  const modalTitle = document.getElementById('modal-title');
  const modalBody = document.getElementById('modal-body');
  const markAsProcessedBtn = document.getElementById('mark-as-processed');

  modalTitle.textContent = 'Terminanfrage Details';

  // Format date
  const requestDate = new Date(request.timestamp);
  const formattedDate = requestDate.toLocaleDateString('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  modalBody.innerHTML = `
    <div class="detail-item">
      <span class="detail-label">Eingegangen am</span>
      <div class="detail-value">${formattedDate}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Name</span>
      <div class="detail-value">${request.name}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Unternehmen</span>
      <div class="detail-value">${request.company || '-'}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">E-Mail</span>
      <div class="detail-value"><a href="mailto:${request.email}">${request.email}</a></div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Telefon</span>
      <div class="detail-value"><a href="tel:${request.phone}">${request.phone}</a></div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Gewünschter Termin</span>
      <div class="detail-value">${request.date}, ${request.time}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Status</span>
      <div class="detail-value">${request.processed ? 'Bearbeitet' : 'Neu'}</div>
    </div>
  `;

  // Update mark as processed button
  if (request.processed) {
    markAsProcessedBtn.textContent = 'Als unbearbeitet markieren';
  } else {
    markAsProcessedBtn.textContent = 'Als bearbeitet markieren';
  }

  markAsProcessedBtn.onclick = function() {
    toggleMeetingProcessedStatus(request);
    modal.style.display = 'none';
  };

  // Show modal
  modal.style.display = 'block';
}

// Show contact form details in modal
function showContactDetails(form) {
  const modal = document.getElementById('detail-modal');
  const modalTitle = document.getElementById('modal-title');
  const modalBody = document.getElementById('modal-body');
  const markAsProcessedBtn = document.getElementById('mark-as-processed');

  modalTitle.textContent = 'Kontaktanfrage Details';

  // Format date
  const formDate = new Date(form.timestamp);
  const formattedDate = formDate.toLocaleDateString('de-DE', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  modalBody.innerHTML = `
    <div class="detail-item">
      <span class="detail-label">Eingegangen am</span>
      <div class="detail-value">${formattedDate}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Name</span>
      <div class="detail-value">${form.name}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">E-Mail</span>
      <div class="detail-value">${form.email}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Betreff</span>
      <div class="detail-value">${form.subject}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Nachricht</span>
      <div class="detail-value">${form.message}</div>
    </div>
    <div class="detail-item">
      <span class="detail-label">Status</span>
      <div class="detail-value">${form.processed ? 'Bearbeitet' : 'Neu'}</div>
    </div>
  `;

  // Update mark as processed button
  if (form.processed) {
    markAsProcessedBtn.textContent = 'Als unbearbeitet markieren';
  } else {
    markAsProcessedBtn.textContent = 'Als bearbeitet markieren';
  }

  markAsProcessedBtn.onclick = function() {
    toggleContactProcessedStatus(form);
    modal.style.display = 'none';
  };

  // Show modal
  modal.style.display = 'block';
}

// Toggle processed status for meeting request
function toggleMeetingProcessedStatus(request) {
  // Get all meeting requests
  let meetingRequests = JSON.parse(localStorage.getItem('meetingRequests')) || [];

  // Find the request and toggle its processed status
  const index = meetingRequests.findIndex(r =>
    r.name === request.name &&
    r.timestamp === request.timestamp
  );

  if (index !== -1) {
    meetingRequests[index].processed = !meetingRequests[index].processed;

    // Save updated requests
    localStorage.setItem('meetingRequests', JSON.stringify(meetingRequests));

    // Reload table
    loadMeetingRequests();
  }
}

// Toggle processed status for contact form
function toggleContactProcessedStatus(form) {
  // Get all contact forms
  let contactForms = JSON.parse(localStorage.getItem('contactForms')) || [];

  // Find the form and toggle its processed status
  const index = contactForms.findIndex(f =>
    f.name === form.name &&
    f.timestamp === form.timestamp
  );

  if (index !== -1) {
    contactForms[index].processed = !contactForms[index].processed;

    // Save updated forms
    localStorage.setItem('contactForms', JSON.stringify(contactForms));

    // Reload table
    loadContactForms();
  }
}

// Delete meeting request
function deleteMeetingRequest(index) {
  if (confirm('Sind Sie sicher, dass Sie diese Terminanfrage löschen möchten?')) {
    // Get all meeting requests
    let meetingRequests = JSON.parse(localStorage.getItem('meetingRequests')) || [];

    // Remove the request at the specified index
    meetingRequests.splice(index, 1);

    // Save updated requests
    localStorage.setItem('meetingRequests', JSON.stringify(meetingRequests));

    // Reload table
    loadMeetingRequests();
  }
}

// Delete contact form
function deleteContactForm(index) {
  if (confirm('Sind Sie sicher, dass Sie diese Kontaktanfrage löschen möchten?')) {
    // Get all contact forms
    let contactForms = JSON.parse(localStorage.getItem('contactForms')) || [];

    // Remove the form at the specified index
    contactForms.splice(index, 1);

    // Save updated forms
    localStorage.setItem('contactForms', JSON.stringify(contactForms));

    // Reload table
    loadContactForms();
  }
}
