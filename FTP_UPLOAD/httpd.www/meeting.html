<!DOCTYPE html>
<html lang="de">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Erstgespräch buchen - Heart & Soul Film- und Medienproduktion</title>
  <link rel="stylesheet" href="css/style_fixed.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="css/form.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.css">
  <link rel="stylesheet" href="css/locomotive-scroll-basic.css">
  <link rel="stylesheet" href="css/animations.css">
</head>

<body data-scroll-container>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <a href="index.html"><img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion"></a>
      </div>
      <div class="nav-links">
        <a href="index.html">Home</a>
        <a href="portfolio.html">Portfolio</a>
        <a href="leistungen.html">Leistungen</a>
        <a href="kontakt.html">Kontakt</a>
      </div>
      <button class="mobile-menu-btn">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </nav>
  <!-- Mobile Menu (separate from navbar for better structure) -->
  <div class="mobile-menu">
    <a href="index.html">Home</a>
    <a href="portfolio.html">Portfolio</a>
    <a href="leistungen.html">Leistungen</a>
    <a href="kontakt.html">Kontakt</a>
  </div>

  <!-- Meeting Form Section -->
  <section class="form-section" data-scroll-section>
    <div class="form-container">
      <div class="progress-container">
        <div class="progress-percentage" id="progress-percentage">0%</div>
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
        </div>
      </div>

      <div class="multi-step-form">
        <!-- Honeypot field (invisible to humans) -->
        <div style="display:none;">
          <input type="text" name="website" id="website" autocomplete="off">
        </div>

        <!-- Step 1: Introduction -->
        <div class="form-step active" id="step-1" data-step="1">
          <div class="step-content">
            <h2 class="form-title">Anfrage für ein kostenloses Erstgespräch</h2>
            <p class="form-subtitle">15 Minuten, um Ihre Ideen und Fragen zu besprechen</p>
            <div class="step-description">
              <p>Wir freuen uns darauf, Sie kennenzulernen und mehr über Ihr Projekt zu erfahren. In diesem kurzen Gespräch können wir erste Ideen austauschen und Ihre Fragen beantworten.</p>
            </div>
            <div class="form-buttons text-center">
              <button type="button" class="btn btn-primary next-step">Termin anfragen</button>
            </div>
          </div>
        </div>

        <!-- Step 2: Name -->
        <div class="form-step" id="step-2" data-step="2">
          <div class="step-content">
            <h3 class="step-title">Wie dürfen wir Sie ansprechen?</h3>
            <div class="form-group">
              <label for="name">Ihr Name *</label>
              <input type="text" id="name" name="name" required placeholder="Vor- und Nachname">
            </div>
            <div class="form-buttons">
              <button type="button" class="btn btn-outline prev-step">Zurück</button>
              <button type="button" class="btn btn-primary next-step">Weiter</button>
            </div>
          </div>
        </div>

        <!-- Step 3: Company (Optional) -->
        <div class="form-step" id="step-3" data-step="3">
          <div class="step-content">
            <h3 class="step-title">Für welches Unternehmen sind Sie tätig?</h3>
            <p class="step-description">Diese Angabe ist optional.</p>
            <div class="form-group">
              <label for="company">Unternehmen (optional)</label>
              <input type="text" id="company" name="company" placeholder="Name des Unternehmens">
            </div>
            <div class="form-buttons">
              <button type="button" class="btn btn-outline prev-step">Zurück</button>
              <button type="button" class="btn btn-primary next-step">Weiter</button>
            </div>
          </div>
        </div>

        <!-- Step 4: Contact Information -->
        <div class="form-step" id="step-4" data-step="4">
          <div class="step-content">
            <h3 class="step-title">Wie können wir Sie erreichen?</h3>
            <p class="step-description">Bitte geben Sie Ihre Kontaktdaten an.</p>
            <div class="form-group">
              <label for="email">E-Mail *</label>
              <input type="email" id="email" name="email" required placeholder="Ihre E-Mail-Adresse">
            </div>
            <div class="form-group">
              <label for="phone">Telefonnummer *</label>
              <input type="tel" id="phone" name="phone" required placeholder="Ihre Telefonnummer">
            </div>
            <div class="form-buttons">
              <button type="button" class="btn btn-outline prev-step">Zurück</button>
              <button type="button" class="btn btn-primary next-step">Weiter</button>
            </div>
          </div>
        </div>

        <!-- Step 5: Date Selection -->
        <div class="form-step" id="step-5" data-step="5">
          <div class="step-content">
            <h3 class="step-title">Wann passt es Ihnen?</h3>
            <p class="step-description">Bitte wählen Sie ein Datum (mindestens 24 Stunden in der Zukunft).</p>
            <div class="form-group">
              <label for="meeting-date">Datum *</label>
              <input type="text" id="meeting-date" name="meeting-date" required placeholder="Wählen Sie ein Datum" readonly>
            </div>
            <div class="form-buttons">
              <button type="button" class="btn btn-outline prev-step">Zurück</button>
              <button type="button" class="btn btn-primary next-step">Weiter</button>
            </div>
          </div>
        </div>

        <!-- Step 6: Time Selection -->
        <div class="form-step" id="step-6" data-step="6">
          <div class="step-content">
            <h3 class="step-title">Zu welcher Uhrzeit?</h3>
            <p class="step-description">Bitte wählen Sie eine Uhrzeit für Ihr 15-minütiges Gespräch.</p>
            <div class="form-group">
              <label>Uhrzeit *</label>
              <div class="time-slots">
                <div class="time-slot-morning">
                  <h4>Vormittag</h4>
                  <div class="radio-group">
                    <div class="radio-item">
                      <input type="radio" id="time-1" name="meeting-time" value="08:00">
                      <label for="time-1">08:00 - 08:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-2" name="meeting-time" value="09:00">
                      <label for="time-2">09:00 - 09:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-3" name="meeting-time" value="10:00">
                      <label for="time-3">10:00 - 10:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-4" name="meeting-time" value="11:00">
                      <label for="time-4">11:00 - 11:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-5" name="meeting-time" value="12:00">
                      <label for="time-5">12:00 - 12:15 Uhr</label>
                    </div>
                  </div>
                </div>
                <div class="time-slot-afternoon">
                  <h4>Nachmittag</h4>
                  <div class="radio-group">
                    <div class="radio-item">
                      <input type="radio" id="time-6" name="meeting-time" value="13:00">
                      <label for="time-6">13:00 - 13:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-7" name="meeting-time" value="14:00">
                      <label for="time-7">14:00 - 14:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-8" name="meeting-time" value="15:00">
                      <label for="time-8">15:00 - 15:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-9" name="meeting-time" value="16:00">
                      <label for="time-9">16:00 - 16:15 Uhr</label>
                    </div>
                  </div>
                </div>
                <div class="time-slot-evening">
                  <h4>Abend</h4>
                  <div class="radio-group">
                    <div class="radio-item">
                      <input type="radio" id="time-10" name="meeting-time" value="17:00">
                      <label for="time-10">17:00 - 17:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-11" name="meeting-time" value="18:00">
                      <label for="time-11">18:00 - 18:15 Uhr</label>
                    </div>
                    <div class="radio-item">
                      <input type="radio" id="time-12" name="meeting-time" value="19:00">
                      <label for="time-12">19:00 - 19:15 Uhr</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-buttons">
              <button type="button" class="btn btn-outline prev-step">Zurück</button>
              <button type="button" class="btn btn-primary next-step">Termin bestätigen</button>
            </div>
          </div>
        </div>

        <!-- Step 7: Confirmation (90%) -->
        <div class="form-step" id="step-7" data-step="7">
          <div class="step-content">
            <h3 class="step-title">Bestätigen Sie Ihren Termin</h3>
            <div class="confirmation-details">
              <p><strong>Name:</strong> <span id="confirm-name"></span></p>
              <p><strong>Unternehmen:</strong> <span id="confirm-company">-</span></p>
              <p><strong>E-Mail:</strong> <span id="confirm-email"></span></p>
              <p><strong>Telefon:</strong> <span id="confirm-phone"></span></p>
              <p><strong>Datum:</strong> <span id="confirm-date"></span></p>
              <p><strong>Uhrzeit:</strong> <span id="confirm-time"></span></p>
            </div>
            <p class="step-description">Bitte überprüfen Sie Ihre Angaben. Mit dem Klick auf "Termin anfragen" wird Ihre Anfrage an uns gesendet.</p>
            <div class="form-buttons">
              <button type="button" class="btn btn-outline prev-step">Zurück</button>
              <button type="button" class="btn btn-primary submit-form">Termin anfragen</button>
            </div>
          </div>
        </div>

        <!-- Step 8: Thank You (100%) -->
        <div class="form-step" id="step-8" data-step="8">
          <div class="step-content">
            <div class="completion-message">
              <h2 class="form-title">Vielen Dank!</h2>
              <p>Wir haben Ihre Terminanfrage erhalten und werden diese schnellstmöglich bestätigen.</p>
              <div class="email-notification">
                <i class="fas fa-check-circle"></i> Ihre Informationen wurden erfolgreich übermittelt
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <!-- Footer -->
  <footer class="footer" data-scroll-section>
    <div class="footer-container">
      <div>
        <div class="footer-logo">
          <img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion">
        </div>
        <p class="footer-description">Professionelle Videoproduktion mit Herz und Seele. Wir bringen Ihre Botschaft zum Leben.</p>
        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
          <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
          <a href="https://www.linkedin.com/in/malte-strömsdörfer-998b55112" class="social-link" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
        </div>
      </div>
      <div>
        <h3 class="footer-title">Leistungen</h3>
        <ul class="footer-links">
          <li><a href="werbefilm.html">Werbefilme</a></li>
          <li><a href="imagefilm.html">Imagefilme</a></li>
          <li><a href="recruiting.html">Recruitingfilme</a></li>
          <li><a href="leistungen.html">Alle Leistungen</a></li>
          <li><a href="blog.html">Blog</a></li>
        </ul>
      </div>
      <div>
        <h3 class="footer-title">Kontakt</h3>
        <ul class="footer-links">
          <li><i class="fas fa-phone mr-2"></i> <a href="tel:+4917650200617">+49 (0) 176 50200617</a></li>
          <li><i class="fas fa-envelope mr-2"></i> <a href="mailto:<EMAIL>"><EMAIL></a></li>
        </ul>      </div>
    </div>
    <div class="copyright">
      <div class="footer-legal">
        <a href="impressum.html">Impressum</a>
        <a href="datenschutz.html">Datenschutz</a>
        <a href="agb.html">AGB</a>
      </div>
      <div class="copyright-text">
        &copy; 2024 Heart & Soul Medienproduktion UG (haftungsbeschränkt). Alle Rechte vorbehalten.
        
      </div>
    </div>
  </footer>

  <!-- Cookie Consent Banner -->
  <div id="cookie-banner" class="cookie-banner">
    <p>Diese Website verwendet Cookies, um Ihnen ein besseres Nutzungserlebnis zu bieten. Mit der Nutzung unserer Website stimmen Sie der Verwendung von Cookies gemäß unserer <a href="datenschutz.html">Datenschutzerklärung</a> zu.</p>
    <div class="cookie-buttons">
      <button id="cookie-decline" class="cookie-btn cookie-btn-decline">Ablehnen</button>
      <button id="cookie-accept" class="cookie-btn cookie-btn-accept">Akzeptieren</button>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/de.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.js"></script>
  <script src="js/app.js"></script>
  <script src="js/meeting-form.js"></script>
  <script src="js/smooth-scroll-basic.js"></script>
  <script src="js/cookie-consent.js"></script>
</body>
</html>
