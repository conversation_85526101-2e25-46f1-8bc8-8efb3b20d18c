/* Performance optimizations for portfolio videos */

/* Video loading placeholder */
.portfolio-video video {
  background-color: #1a1a1a;
  background-image: linear-gradient(45deg, #1a1a1a 25%, transparent 25%), 
                    linear-gradient(-45deg, #1a1a1a 25%, transparent 25%), 
                    linear-gradient(45deg, transparent 75%, #1a1a1a 75%), 
                    linear-gradient(-45deg, transparent 75%, #1a1a1a 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* Loading spinner for videos */
.portfolio-video::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  margin: -20px 0 0 -20px;
  border: 3px solid rgba(255, 48, 48, 0.3);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-video.loading::before {
  opacity: 1;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Optimize video container */
.portfolio-video {
  position: relative;
  background-color: #000;
  border-radius: 12px;
  overflow: hidden;
}

/* Reduce layout shift */
.portfolio-item {
  min-height: 200px;
}

@media (min-width: 768px) {
  .portfolio-item {
    min-height: 250px;
  }
}

@media (min-width: 1024px) {
  .portfolio-item {
    min-height: 300px;
  }
}
