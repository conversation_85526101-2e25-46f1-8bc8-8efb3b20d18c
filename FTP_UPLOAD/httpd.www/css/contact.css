/* Contact Page Styles */

.contact-section {
  padding: 5rem 0;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  padding: 0 1.5rem;
}

/* Contact Info Styles */
.contact-info {
  padding-right: 2rem;
}

.contact-info h2 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 0.8rem;
}

.contact-info h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

.contact-info > p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-gray);
  margin-bottom: 2.5rem;
}

.contact-details {
  margin-bottom: 3rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.8rem;
}

.contact-item i {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-right: 1.2rem;
  margin-top: 0.3rem;
}

.contact-item h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--text-light);
}

.contact-item p {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-gray);
}

.contact-item a {
  color: var(--text-gray);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-item a:hover {
  color: var(--primary-color);
}

.social-contact h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  color: var(--text-light);
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
}

/* Contact Form Styles */
.contact-form-container {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 2.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.contact-form-container h2 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 0.8rem;
}

.contact-form-container h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

.contact-form-container > p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-gray);
  margin-bottom: 2rem;
}

.contact-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-group {
  position: relative;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  color: var(--text-light);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.8rem 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  color: var(--text-light);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: rgba(255, 48, 48, 0.5);
  background-color: rgba(255, 255, 255, 0.07);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.checkbox-container {
  display: flex;
  align-items: flex-start;
  margin-top: 0.5rem;
}

.checkbox-container input[type="checkbox"] {
  width: auto;
  margin-right: 0.8rem;
  margin-top: 0.3rem;
}

.checkbox-container label {
  margin-bottom: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.checkbox-container a {
  color: var(--primary-color);
  text-decoration: none;
}

.checkbox-container a:hover {
  text-decoration: underline;
}

.contact-form button {
  margin-top: 1rem;
  padding: 1rem 2rem;
  width: auto;
  justify-self: start;
}

/* Map Section */
.map-section {
  padding: 3rem 0 5rem;
  background-color: rgba(255, 255, 255, 0.01);
}

.map-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.map-container h2 {
  font-size: 2.2rem;
  margin-bottom: 2rem;
  color: var(--text-light);
  text-align: center;
}

.map-wrapper {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.map-wrapper iframe {
  display: block;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .contact-container {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-info {
    padding-right: 0;
  }
}

@media (max-width: 768px) {
  .contact-section {
    padding: 4rem 0;
  }

  .contact-form {
    grid-template-columns: 1fr;
  }

  .form-group.full-width {
    grid-column: span 1;
  }

  .contact-form-container {
    padding: 2rem 1.5rem;
  }

  .map-section {
    padding: 2rem 0 4rem;
  }

  .map-container h2 {
    font-size: 1.8rem;
  }
}

/* Form Success Message */
.form-success-message {
  text-align: center;
  padding: 3rem 2rem;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.success-icon {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.form-success-message h3 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.form-success-message p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--text-gray);
}
