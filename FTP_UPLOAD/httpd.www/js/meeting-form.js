document.addEventListener('DOMContentLoaded', function() {
  // Form elements
  const form = document.querySelector('.multi-step-form');
  const steps = document.querySelectorAll('.form-step');
  const nextButtons = document.querySelectorAll('.next-step');
  const prevButtons = document.querySelectorAll('.prev-step');
  const submitButton = document.querySelector('.submit-form');
  const progressFill = document.getElementById('progress-fill');
  const progressPercentage = document.getElementById('progress-percentage');

  // Form fields
  const nameInput = document.getElementById('name');
  const companyInput = document.getElementById('company');
  const emailInput = document.getElementById('email');
  const phoneInput = document.getElementById('phone');
  const dateInput = document.getElementById('meeting-date');
  const timeInputs = document.querySelectorAll('input[name="meeting-time"]');

  // Confirmation elements
  const confirmName = document.getElementById('confirm-name');
  const confirmCompany = document.getElementById('confirm-company');
  const confirmEmail = document.getElementById('confirm-email');
  const confirmPhone = document.getElementById('confirm-phone');
  const confirmDate = document.getElementById('confirm-date');
  const confirmTime = document.getElementById('confirm-time');

  // Initialize date picker
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);

  flatpickr(dateInput, {
    locale: 'de',
    dateFormat: 'd.m.Y',
    minDate: tomorrow,
    disable: [
      function(date) {
        // Disable weekends
        return (date.getDay() === 0 || date.getDay() === 6);
      }
    ]
  });

  // Update progress bar
  function updateProgress(step) {
    const totalSteps = steps.length;
    const currentStep = parseInt(step);
    let percentage;

    // Custom percentages for specific steps
    switch(currentStep) {
      case 1:
        percentage = 0;
        break;
      case 2:
        percentage = 20;
        break;
      case 3:
        percentage = 40;
        break;
      case 4:
        percentage = 50;
        break;
      case 5:
        percentage = 67;
        break;
      case 6:
        percentage = 90;
        break;
      case 7:
        percentage = 90;
        break;
      case 8:
        percentage = 100;
        break;
      default:
        percentage = Math.round((currentStep - 1) / (totalSteps - 1) * 100);
    }

    progressFill.style.width = `${percentage}%`;
    progressPercentage.textContent = `${percentage}%`;

    // If we're at 90%, prepare to send data
    if (percentage === 90) {
      updateConfirmationDetails();
    }
  }

  // Update confirmation details
  function updateConfirmationDetails() {
    // Make sure we update the confirmation details before showing the confirmation step
    if (confirmName) confirmName.textContent = nameInput.value || '';
    if (confirmCompany) confirmCompany.textContent = companyInput.value || '-';
    if (confirmEmail) confirmEmail.textContent = emailInput.value || '';
    if (confirmPhone) confirmPhone.textContent = phoneInput.value || '';
    if (confirmDate) confirmDate.textContent = dateInput.value || '';

    let selectedTime = '';
    timeInputs.forEach(input => {
      if (input.checked) {
        const label = document.querySelector(`label[for="${input.id}"]`);
        if (label) {
          selectedTime = label.textContent;
        }
      }
    });

    if (confirmTime) confirmTime.textContent = selectedTime;

    // Debug output to console
    console.log('Updating confirmation details:');
    console.log('Name:', nameInput.value);
    console.log('Company:', companyInput.value);
    console.log('Email:', emailInput.value);
    console.log('Phone:', phoneInput.value);
    console.log('Date:', dateInput.value);
    console.log('Selected Time:', selectedTime);
  }

  // Validate current step
  function validateStep(step) {
    const currentStep = parseInt(step);

    switch(currentStep) {
      case 2: // Name step
        return nameInput.value.trim() !== '';
      case 4: // Contact Information step
        return emailInput.value.trim() !== '' && phoneInput.value.trim() !== '';
      case 5: // Date step
        return dateInput.value.trim() !== '';
      case 6: // Time step
        let timeSelected = false;
        timeInputs.forEach(input => {
          if (input.checked) timeSelected = true;
        });
        return timeSelected;
      default:
        return true;
    }
  }

  // Show error message
  function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.textContent = message;

    const activeStep = document.querySelector('.form-step.active');
    const formGroup = activeStep.querySelector('.form-group');

    // Remove any existing error messages
    const existingError = activeStep.querySelector('.error-message');
    if (existingError) existingError.remove();

    formGroup.appendChild(errorDiv);

    // Remove error after 3 seconds
    setTimeout(() => {
      errorDiv.remove();
    }, 3000);
  }

  // Navigate to next step
  function goToNextStep() {
    const activeStep = document.querySelector('.form-step.active');
    const currentStep = activeStep.dataset.step;

    if (!validateStep(currentStep)) {
      let errorMessage = '';

      switch(parseInt(currentStep)) {
        case 2:
          errorMessage = 'Bitte geben Sie Ihren Namen ein.';
          break;
        case 4:
          errorMessage = 'Bitte geben Sie Ihre E-Mail und Telefonnummer ein.';
          break;
        case 5:
          errorMessage = 'Bitte wählen Sie ein Datum aus.';
          break;
        case 6:
          errorMessage = 'Bitte wählen Sie eine Uhrzeit aus.';
          break;
      }

      showError(errorMessage);
      return;
    }

    const nextStep = parseInt(currentStep) + 1;

    // If moving to confirmation step, update the confirmation details
    if (nextStep === 7) {
      updateConfirmationDetails();
    }

    if (nextStep <= steps.length) {
      activeStep.classList.remove('active');
      document.getElementById(`step-${nextStep}`).classList.add('active');
      updateProgress(nextStep);
    }
  }

  // Navigate to previous step
  function goToPrevStep() {
    const activeStep = document.querySelector('.form-step.active');
    const currentStep = activeStep.dataset.step;
    const prevStep = parseInt(currentStep) - 1;

    if (prevStep >= 1) {
      activeStep.classList.remove('active');
      document.getElementById(`step-${prevStep}`).classList.add('active');
      updateProgress(prevStep);
    }
  }

  // Submit form
  function submitForm() {
    // Get selected time label text
    let selectedTimeText = '';
    timeInputs.forEach(input => {
      if (input.checked) {
        const label = document.querySelector(`label[for="${input.id}"]`);
        if (label) {
          selectedTimeText = label.textContent;
        }
      }
    });

    // Collect form data
    const formData = {
      name: nameInput.value,
      company: companyInput.value,
      email: emailInput.value,
      phone: phoneInput.value,
      date: dateInput.value,
      time: selectedTimeText,
      timestamp: new Date().toISOString(),
      processed: false
    };

    // Store in localStorage
    let meetingRequests = JSON.parse(localStorage.getItem('meetingRequests')) || [];
    meetingRequests.push(formData);
    localStorage.setItem('meetingRequests', JSON.stringify(meetingRequests));

    console.log('Form data stored:', formData);

    // Send <NAME_EMAIL>
    const emailData = new FormData();
    emailData.append('form_type', 'meeting');
    emailData.append('form_data', JSON.stringify(formData));
    emailData.append('website', ''); // Honeypot field

    // Send the data to the server
    fetch('/send_form.php', {
      method: 'POST',
      body: emailData
    })
    .then(response => response.json())
    .then(data => {
      console.log('Email sent:', data);

      // Move to thank you step (100%) regardless of email success
      // This ensures the user experience is not affected by email issues
      const activeStep = document.querySelector('.form-step.active');
      activeStep.classList.remove('active');
      document.getElementById('step-8').classList.add('active');
      updateProgress(8);
    })
    .catch(error => {
      console.error('Error sending email:', error);

      // Still move to thank you step even if there's an error
      const activeStep = document.querySelector('.form-step.active');
      activeStep.classList.remove('active');
      document.getElementById('step-8').classList.add('active');
      updateProgress(8);
    });
  }

  // Event listeners
  nextButtons.forEach(button => {
    button.addEventListener('click', goToNextStep);
  });

  prevButtons.forEach(button => {
    button.addEventListener('click', goToPrevStep);
  });

  if (submitButton) {
    submitButton.addEventListener('click', submitForm);
  }

  // Initialize progress
  updateProgress(1);

  // Add CSS for error messages
  const style = document.createElement('style');
  style.textContent = `
    .error-message {
      color: var(--primary-color);
      margin-top: 0.5rem;
      font-size: 0.9rem;
      animation: fadeIn 0.3s ease;
    }

    .time-slots {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
    }

    @media (max-width: 768px) {
      .time-slots {
        grid-template-columns: 1fr;
      }
    }
  `;
  document.head.appendChild(style);
});
