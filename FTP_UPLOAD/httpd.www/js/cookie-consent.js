document.addEventListener('DOMContentLoaded', function() {
  // Check if user has already made a choice
  const cookieConsent = getCookie('cookie-consent');
  
  if (cookieConsent === null) {
    // If no choice has been made, show the banner after a short delay
    setTimeout(function() {
      const cookieBanner = document.getElementById('cookie-banner');
      if (cookieBanner) {
        cookieBanner.classList.add('show');
      }
    }, 1500);
  }
  
  // Accept button click handler
  const acceptBtn = document.getElementById('cookie-accept');
  if (acceptBtn) {
    acceptBtn.addEventListener('click', function() {
      setCookie('cookie-consent', 'accepted', 365); // Store consent for 1 year
      hideBanner();
    });
  }
  
  // Decline button click handler
  const declineBtn = document.getElementById('cookie-decline');
  if (declineBtn) {
    declineBtn.addEventListener('click', function() {
      setCookie('cookie-consent', 'declined', 365); // Store decision for 1 year
      hideBanner();
    });
  }
  
  // Function to hide the banner with animation
  function hideBanner() {
    const cookieBanner = document.getElementById('cookie-banner');
    if (cookieBanner) {
      cookieBanner.style.opacity = '0';
      cookieBanner.style.transform = 'translateY(100px)';
      
      setTimeout(function() {
        cookieBanner.classList.remove('show');
      }, 500);
    }
  }
  
  // Helper function to set a cookie
  function setCookie(name, value, days) {
    let expires = '';
    if (days) {
      const date = new Date();
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
      expires = '; expires=' + date.toUTCString();
    }
    document.cookie = name + '=' + (value || '') + expires + '; path=/; SameSite=Lax';
  }
  
  // Helper function to get a cookie
  function getCookie(name) {
    const nameEQ = name + '=';
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
  }
});
