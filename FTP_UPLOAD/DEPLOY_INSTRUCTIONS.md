# 🚀 DEPLOY INSTRUCTIONS - Heart & Soul V2

## ⚡ **QUICK DEPLOYMENT**

### **1. FTP Upload Commands**

#### **Option A: FTP Client (Recommended)**
```
1. Open your FTP client (File<PERSON><PERSON>, Cyberduck, etc.)
2. Connect to your server
3. Navigate to root directory
4. Upload FTP_UPLOAD/httpd.www/ → /httpd.www/
5. Upload FTP_UPLOAD/httpd.private/ → /httpd.private/
6. Set permissions: 755 for httpd.www, 700 for httpd.private
```

#### **Option B: Command Line**
```bash
# Upload public files
scp -r FTP_UPLOAD/httpd.www/* user@server:/path/to/httpd.www/

# Upload private files  
scp -r FTP_UPLOAD/httpd.private/* user@server:/path/to/httpd.private/

# Set permissions
ssh user@server "chmod -R 755 /path/to/httpd.www"
ssh user@server "chmod -R 700 /path/to/httpd.private"
```

---

## 🎯 **CRITICAL FILES TO VERIFY**

### **✅ Must Upload Successfully**
```
httpd.www/index.html          # Fixed hero video
httpd.www/portfolio.html      # 10 working videos
httpd.www/js/app.js          # Fixed conflicts
httpd.www/js/portfolio.js    # Enhanced loading
httpd.www/videos/*.webm      # All video files
```

---

## 🧪 **IMMEDIATE TESTING**

### **1. Quick Test (2 minutes)**
```
✅ https://heartsoulmedia.de/
   → Hero video autoplays
   
✅ https://heartsoulmedia.de/portfolio.html
   → Scroll down, videos load
   → Click play on any video
```

### **2. Full Test (5 minutes)**
```
✅ Test all 10 portfolio videos
✅ Test mobile responsiveness
✅ Test different browsers
✅ Check console for errors
```

---

## 🚨 **TROUBLESHOOTING**

### **If Videos Don't Load:**
1. Check browser console for errors
2. Verify video files uploaded correctly
3. Check file permissions (755)
4. Test direct video URL access

### **If JavaScript Errors:**
1. Clear browser cache
2. Check if all JS files uploaded
3. Verify file paths are correct

---

## 📞 **SUPPORT CHECKLIST**

### **Before Deployment:**
- [ ] Backup current website
- [ ] Test FTP connection
- [ ] Verify upload permissions

### **During Deployment:**
- [ ] Upload all files successfully
- [ ] Set correct permissions
- [ ] Verify file sizes match

### **After Deployment:**
- [ ] Test homepage videos
- [ ] Test portfolio videos
- [ ] Test mobile experience
- [ ] Check browser console

---

## ✅ **SUCCESS CRITERIA**

### **🎬 Videos Working:**
- ✅ Hero video autoplays on homepage
- ✅ All 10 portfolio videos load and play
- ✅ Video controls work (play, pause, fullscreen)
- ✅ Only one video plays at a time

### **🚀 Performance:**
- ✅ Fast page loading
- ✅ Smooth video transitions
- ✅ Mobile responsive

### **🔧 Technical:**
- ✅ No JavaScript errors in console
- ✅ All video files accessible
- ✅ Cross-browser compatibility

---

## 🎉 **DEPLOYMENT COMPLETE**

Once uploaded, your Heart & Soul website will have:
- ✅ **Working portfolio videos**
- ✅ **Optimized performance**
- ✅ **Enhanced user experience**
- ✅ **Cross-browser compatibility**

**The portfolio video issues will be completely resolved!**
