<?php
// Start session for CSRF protection
session_start();

// Generate CSRF token
$csrf_token = bin2hex(random_bytes(32));
$_SESSION['csrf_token'] = $csrf_token;

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
?>
<!DOCTYPE html>
<html lang="de">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="csrf-token" content="<?php echo htmlspecialchars($csrf_token); ?>" id="csrf-token">
  <title>Admin Dashboard - Heart & Soul Film- und Medienproduktion</title>
  <link rel="stylesheet" href="../css/style_fixed.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="../css/form.css">
  <link rel="stylesheet" href="css/admin.css">
  
  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'; style-src 'self' https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com;">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
</head>

<body>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <a href="../index.html"><img src="../HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion"></a>
      </div>
      <div class="nav-links">
        <a href="../index.html">Website</a>
        <a href="#" class="active">Dashboard</a>
        <a href="#" id="logout-btn">Abmelden</a>
      </div>
    </div>
  </nav>

  <!-- Dashboard Section -->
  <section class="admin-section">
    <div class="admin-container">
      <h2 class="admin-title">Admin Dashboard</h2>

      <div class="admin-tabs">
        <button class="tab-btn active" data-tab="meeting-requests">Terminanfragen</button>
        <button class="tab-btn" data-tab="contact-forms">Kontaktanfragen</button>
      </div>

      <div class="tab-content active" id="meeting-requests">
        <div class="admin-card">
          <h3 class="card-title">Terminanfragen</h3>
          <div class="filter-controls">
            <div class="search-box">
              <input type="text" id="meeting-search" placeholder="Suchen...">
              <i class="fas fa-search"></i>
            </div>
            <div class="filter-dropdown">
              <select id="meeting-filter">
                <option value="all">Alle anzeigen</option>
                <option value="new">Neue Anfragen</option>
                <option value="processed">Bearbeitete Anfragen</option>
              </select>
            </div>
          </div>

          <div class="data-table-container">
            <table class="data-table" id="meeting-table">
              <thead>
                <tr>
                  <th>Datum</th>
                  <th>Name</th>
                  <th>Unternehmen</th>
                  <th>E-Mail</th>
                  <th>Telefon</th>
                  <th>Termin</th>
                  <th>Status</th>
                  <th>Aktionen</th>
                </tr>
              </thead>
              <tbody id="meeting-table-body">
                <!-- Data will be loaded dynamically -->
              </tbody>
            </table>
            <div id="meeting-empty-state" class="empty-state">
              <i class="fas fa-calendar-times"></i>
              <p>Keine Terminanfragen vorhanden</p>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-content" id="contact-forms">
        <div class="admin-card">
          <h3 class="card-title">Kontaktanfragen</h3>
          <div class="filter-controls">
            <div class="search-box">
              <input type="text" id="contact-search" placeholder="Suchen...">
              <i class="fas fa-search"></i>
            </div>
            <div class="filter-dropdown">
              <select id="contact-filter">
                <option value="all">Alle anzeigen</option>
                <option value="new">Neue Anfragen</option>
                <option value="processed">Bearbeitete Anfragen</option>
              </select>
            </div>
          </div>

          <div class="data-table-container">
            <table class="data-table" id="contact-table">
              <thead>
                <tr>
                  <th>Datum</th>
                  <th>Name</th>
                  <th>E-Mail</th>
                  <th>Betreff</th>
                  <th>Status</th>
                  <th>Aktionen</th>
                </tr>
              </thead>
              <tbody id="contact-table-body">
                <!-- Data will be loaded dynamically -->
              </tbody>
            </table>
            <div id="contact-empty-state" class="empty-state">
              <i class="fas fa-inbox"></i>
              <p>Keine Kontaktanfragen vorhanden</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Detail Modal -->
  <div class="modal" id="detail-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3 id="modal-title">Anfrage Details</h3>
        <button class="close-modal">&times;</button>
      </div>
      <div class="modal-body" id="modal-body">
        <!-- Details will be loaded dynamically -->
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline close-modal">Schließen</button>
        <button class="btn btn-primary" id="mark-as-processed">Als bearbeitet markieren</button>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-container">
      <div>
        <div class="footer-logo">
          <img src="../HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion">
        </div>
        <p class="footer-description">Administrationsbereich</p>
      </div>
    </div>
    <div class="copyright">
      &copy; 2024 Heart & Soul Medienproduktion UG (haftungsbeschränkt). Alle Rechte vorbehalten.
    </div>
  </footer>

  <!-- Scripts -->
  <script src="js/admin-auth.js"></script>
  <script src="js/admin-dashboard.js"></script>
  <script src="js/email-sender.js"></script>
</body>

</html>
