<?php
session_start();

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Verify token parameter
$token = $_GET['token'] ?? '';
if (empty($token)) {
    header('Location: login.html');
    exit;
}
?>
<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passwort zurücksetzen - Heart & Soul Film- und Medienproduktion</title>
    <link rel="stylesheet" href="../css/style_fixed.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="../css/form.css">
    <link rel="stylesheet" href="css/admin.css">
    
    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self'; style-src 'self' https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com;">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="navbar-container">
            <div class="logo">
                <a href="../index.html"><img src="../HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion"></a>
            </div>
            <div class="nav-links">
                <a href="../index.html">Zurück zur Website</a>
            </div>
        </div>
    </nav>

    <!-- Reset Password Form Section -->
    <section class="form-section">
        <div class="form-container admin-login-container">
            <h2 class="form-title">Passwort zurücksetzen</h2>
            <p class="form-subtitle">Bitte geben Sie Ihr neues Passwort ein</p>

            <div id="reset-error" class="error-message" style="display: none; margin-bottom: 2rem;">
                <i class="fas fa-exclamation-circle"></i> <span id="error-text"></span>
            </div>

            <div id="reset-success" class="success-message" style="display: none; margin-bottom: 2rem;">
                <i class="fas fa-check-circle"></i> <span id="success-text"></span>
            </div>

            <form id="reset-form" class="admin-form">
                <input type="hidden" id="reset-token" value="<?php echo htmlspecialchars($token); ?>">

                <div class="form-group password-group">
                    <label for="password">Neues Passwort</label>
                    <div class="password-input-container">
                        <input type="password" id="password" name="password" required minlength="12">
                        <button type="button" class="password-toggle" aria-label="Passwort anzeigen">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="password-requirements">Mindestens 12 Zeichen, Groß- und Kleinbuchstaben, Zahlen und Sonderzeichen</small>
                </div>

                <div class="form-group password-group">
                    <label for="confirm-password">Passwort bestätigen</label>
                    <div class="password-input-container">
                        <input type="password" id="confirm-password" name="confirm-password" required minlength="12">
                        <button type="button" class="password-toggle" aria-label="Passwort anzeigen">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-buttons text-center">
                    <button type="submit" class="btn btn-primary">Passwort zurücksetzen</button>
                </div>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div>
                <div class="footer-logo">
                    <img src="../HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion">
                </div>
                <p class="footer-description">Administrationsbereich</p>
            </div>
        </div>
        <div class="copyright">
            &copy; 2024 Heart & Soul Medienproduktion UG (haftungsbeschränkt). Alle Rechte vorbehalten.
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/reset-password.js"></script>
</body>

</html>