-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON>gin attempts table for rate limiting
CREATE TABLE login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip VARCHAR(45) NOT NULL,
    success BOOLEAN NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_ip_timestamp (ip, timestamp)
);

-- Password reset tokens table
CREATE TABLE password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VA<PERSON>HA<PERSON>(255) NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    expires TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_token (token),
    INDEX idx_email (email)
);

-- Create admin user (password should be changed immediately after installation)
INSERT INTO users (username, email, password_hash) VALUES 
('admin', '<EMAIL>', '$2y$10$YourHashedPasswordHere');

-- Create cleanup procedure for old records
DELIMITER //

CREATE PROCEDURE cleanup_old_records()
BEGIN
    -- Delete old login attempts (older than 24 hours)
    DELETE FROM login_attempts 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
    -- Delete old password reset tokens (older than 24 hours)
    DELETE FROM password_resets 
    WHERE (expires < NOW() OR used = TRUE) 
    AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);
END //

DELIMITER ;

-- Create event to run cleanup procedure daily
CREATE EVENT cleanup_old_records_event
    ON SCHEDULE EVERY 1 DAY
    DO CALL cleanup_old_records();