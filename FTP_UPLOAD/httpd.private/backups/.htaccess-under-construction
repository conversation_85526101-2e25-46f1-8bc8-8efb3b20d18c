# Under Construction Mode
# Rename this file to .htaccess to activate under construction mode
# Rename back to .htaccess-under-construction to go live

# Security Headers (keep existing ones)
Header always set X-XSS-Protection "1; mode=block"
Header always set X-Content-Type-Options "nosniff"
Header always set X-Frame-Options "DENY"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' https:; connect-src 'self' https:; frame-src 'self' https://www.youtube.com https://player.vimeo.com;"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"

# PHP Settings (keep existing ones)
php_flag display_errors Off
php_flag log_errors On
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value session.gc_maxlifetime 1800
php_value session.cookie_secure 1
php_value session.cookie_httponly 1

# Prevent directory listing
Options -Indexes

# Protect sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Under Construction Redirect
# Redirect all visitors to under construction page except for specific IPs or admin access
RewriteEngine On

# Allow access to admin directory
RewriteCond %{REQUEST_URI} !^/admin/

# Allow access to assets (CSS, JS, images, videos)
RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|ico|svg|webp|mp4|webm|pdf|woff|woff2|ttf|eot)$ [NC]

# Allow access to under-construction.html itself
RewriteCond %{REQUEST_URI} !^/under-construction\.html$

# Allow access with special parameter for testing (add ?test=true to any URL)
RewriteCond %{QUERY_STRING} !test=true

# Redirect everything else to under construction page
RewriteRule ^(.*)$ /under-construction.html [R=302,L]

# Optional: Allow specific IP addresses to bypass (uncomment and add your IP)
# RewriteCond %{REMOTE_ADDR} !^123\.456\.789\.012$
# RewriteCond %{REMOTE_ADDR} !^987\.654\.321\.098$