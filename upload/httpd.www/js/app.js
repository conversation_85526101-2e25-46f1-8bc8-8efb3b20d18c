document.addEventListener('DOMContentLoaded', function() {
  // Ensure hero video plays properly
  const heroVideo = document.querySelector('.hero-video');
  if (heroVideo) {
    // Force load and play the hero video
    heroVideo.load();

    // Make sure autoplay works
    heroVideo.play().catch(error => {
      console.log('Hero video autoplay failed:', error);
      // Add a play button if autoplay fails
      const heroSection = document.querySelector('.hero-video-section');
      const playButton = document.createElement('button');
      playButton.innerHTML = '<i class="fas fa-play"></i>';
      playButton.className = 'hero-play-button';
      playButton.addEventListener('click', () => {
        heroVideo.play();
        playButton.style.display = 'none';
      });
      heroSection.appendChild(playButton);
    });
  }

  // Initialisiere Plyr für alle Videos außer dem Hero-Video und Portfolio-Videos
  // Portfolio videos are handled by portfolio.js
  const isPortfolioPage = window.location.pathname.includes('portfolio.html');

  if (!isPortfolioPage) {
    const players = Array.from(document.querySelectorAll('video:not(.hero-video)')).map(p => {
      console.log('Initializing Plyr for video:', p.querySelector('source')?.src || 'no source');
      return new Plyr(p, {
        controls: [
          'play-large',
          'play',
          'progress',
          'current-time',
          'mute',
          'volume',
          'fullscreen'
        ],
        iconUrl: 'https://cdn.plyr.io/3.7.8/plyr.svg',
        blankVideo: 'https://cdn.plyr.io/static/blank.mp4',
        autoplay: false
      });
    });
    console.log('Initialized', players.length, 'Plyr players');
  }

  // Transparente Navigation beim Scrollen
  const navbar = document.querySelector('.navbar');
  const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
  const mobileMenu = document.querySelector('.mobile-menu');

  // Scroll-Event für die Navigation
  window.addEventListener('scroll', function() {
    if (window.scrollY > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });

  // Mobile Menü Toggle
  mobileMenuBtn.addEventListener('click', function() {
    mobileMenu.classList.toggle('active');
  });

  // Schließe das mobile Menü, wenn ein Link geklickt wird
  const mobileLinks = document.querySelectorAll('.mobile-menu a');
  mobileLinks.forEach(link => {
    link.addEventListener('click', function() {
      mobileMenu.classList.remove('active');
    });
  });

  // Handle active state for navigation links
  const navLinks = document.querySelectorAll('.nav-links a');
  navLinks.forEach(link => {
    link.addEventListener('click', function() {
      // Remove active class from all links
      navLinks.forEach(l => l.classList.remove('active'));
      // Add active class to clicked link
      this.classList.add('active');

      // Update mobile menu active state as well
      const href = this.getAttribute('href');
      const mobileLink = document.querySelector(`.mobile-menu a[href="${href}"]`);
      if (mobileLink) {
        document.querySelectorAll('.mobile-menu a').forEach(l => l.classList.remove('active'));
        mobileLink.classList.add('active');
      }
    });
  });



  // Smooth Scroll für Anker-Links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        window.scrollTo({
          top: targetElement.offsetTop - 80, // Offset für die Navigation
          behavior: 'smooth'
        });
      }
    });
  });

  // Einfaches Formular-Handling
  const contactForm = document.getElementById('contact-form');
  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();
      // Hier könnte später eine echte Formularverarbeitung implementiert werden
      alert('Vielen Dank für Ihre Nachricht! Wir werden uns in Kürze bei Ihnen melden.');
      contactForm.reset();
    });
  }

  // Animationen beim Scrollen
  const animateOnScroll = function() {
    const elements = document.querySelectorAll('.product-card, .video-item, .section-title');

    elements.forEach(element => {
      const elementPosition = element.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;

      if (elementPosition < windowHeight - 100) {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
      }
    });
  };

  // Initialisiere Animationen
  window.addEventListener('scroll', animateOnScroll);
  animateOnScroll(); // Führe beim Laden der Seite aus
});